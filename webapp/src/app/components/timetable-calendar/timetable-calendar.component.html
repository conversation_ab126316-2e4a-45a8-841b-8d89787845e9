<div class="timetable-calendar-container" [class.rtl]="isRtl">
  <!-- رأس التقويم -->
  <div class="calendar-header mb-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4 space-x-reverse">
        <div class="flex items-center space-x-2 space-x-reverse">
          <hugeicons-icon
            [icon]="Calendar01StrokeRounded"
            [size]="20"
            [strokeWidth]="1.5"
            class="text-indigo-600">
          </hugeicons-icon>
          <h3 class="text-xl font-semibold text-gray-900">الجدول الأسبوعي</h3>
        </div>
        <div class="flex items-center space-x-2 space-x-reverse">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                {{ getLectureText(totalEvents()) }}
              </span>
          <span
            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                {{ getHourText(totalCreditHours()) }}
              </span>
          <span *ngIf="conflictsCount() > 0"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                <hugeicons-icon
                  [icon]="Alert01StrokeRounded"
                  [size]="16"
                  [strokeWidth]="1.5"
                  class="text-red-600 ml-1">
                </hugeicons-icon>
                {{ getConflictText(conflictsCount()) }}
              </span>
        </div>
      </div>

      <div class="flex items-center space-x-3 space-x-reverse">
        <button (click)="toggleSettingsDrawer()"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4
                     font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none
                     focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <hugeicons-icon
            [icon]="Settings02StrokeRounded"
            [size]="16"
            [strokeWidth]="1.5"
            class="ml-1">
          </hugeicons-icon>
          إعدادات
        </button>
        <!-- Export Dropdown -->
        <div class="relative" data-export-dropdown>
          <button (click)="toggleExportDropdown()"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4
                       font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none
                       focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <hugeicons-icon
              [icon]="Download01StrokeRounded"
              [size]="16"
              [strokeWidth]="1.5"
              class="ml-1">
            </hugeicons-icon>
            تصدير
            <hugeicons-icon
              [icon]="ArrowDown01StrokeRounded"
              [size]="14"
              [strokeWidth]="1.5"
              class="mr-1"
              [class.rotate-180]="exportDropdownOpen()">
            </hugeicons-icon>
          </button>

          <!-- Dropdown Menu -->
          <div *ngIf="exportDropdownOpen()"
               class="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"
               (click)="$event.stopPropagation()">
            <div class="py-1" role="menu">
              <button (click)="exportAsPNG()"
                      class="group flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      role="menuitem">
                <hugeicons-icon
                  [icon]="Download01StrokeRounded"
                  [size]="16"
                  [strokeWidth]="1.5"
                  class="ml-2 text-gray-400 group-hover:text-gray-500">
                </hugeicons-icon>
                تصدير كصورة (PNG)
              </button>

              <button (click)="exportAsJSON()"
                      class="group flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      role="menuitem">
                <hugeicons-icon
                  [icon]="File02StrokeRounded"
                  [size]="16"
                  [strokeWidth]="1.5"
                  class="ml-2 text-gray-400 group-hover:text-gray-500">
                </hugeicons-icon>
                تصدير البيانات (JSON)
              </button>

              <button (click)="exportAsCSV()"
                      class="group flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      role="menuitem">
                <hugeicons-icon
                  [icon]="File02StrokeRounded"
                  [size]="16"
                  [strokeWidth]="1.5"
                  class="ml-2 text-gray-400 group-hover:text-gray-500">
                </hugeicons-icon>
                تصدير جدول (CSV)
              </button>

              <button (click)="exportAsICS()"
                      class="group flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      role="menuitem">
                <hugeicons-icon
                  [icon]="Calendar01StrokeRounded"
                  [size]="16"
                  [strokeWidth]="1.5"
                  class="ml-2 text-gray-400 group-hover:text-gray-500">
                </hugeicons-icon>
                تقويم (ICS)
              </button>

              <div class="border-t border-gray-100"></div>

              <button (click)="importFromJSON()"
                      class="group flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      role="menuitem">
                <hugeicons-icon
                  [icon]="Upload01StrokeRounded"
                  [size]="16"
                  [strokeWidth]="1.5"
                  class="ml-2 text-gray-400 group-hover:text-gray-500">
                </hugeicons-icon>
                استيراد البيانات
              </button>
            </div>
          </div>
        </div>
        <button (click)="printCalendar()"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4
                     font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none
                     focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <hugeicons-icon
            [icon]="PrinterStrokeRounded"
            [size]="16"
            [strokeWidth]="1.5"
            class="ml-1">
          </hugeicons-icon>
          طباعة
        </button>
      </div>
    </div>
  </div>

  <!-- حالة التحميل -->
  <div *ngIf="loading()" class="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
    <div class="text-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
      <p class="text-gray-600">جاري تحضير الجدول...</p>
    </div>
  </div>

  <!-- حالة فارغة -->
  <div *ngIf="!loading() && calendarEvents().length === 0"
       class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
    <div class="text-center">
      <div class="flex items-center justify-center space-x-2 space-x-reverse mb-2">
        <hugeicons-icon
          [icon]="Calendar01StrokeRounded"
          [size]="20"
          [strokeWidth]="1.5"
          class="text-yellow-600">
        </hugeicons-icon>
        <h3 class="text-lg font-medium text-yellow-800">التقويم جاهز ولكن لا توجد مواعيد</h3>
      </div>
      <p class="text-yellow-700">تم تحليل المقررات ولكن لا توجد مواعيد صالحة للعرض في التقويم</p>
    </div>
  </div>

  <!-- التقويم + الشريط الجانبي -->
  <div *ngIf="!loading()" class="calendar-wrapper">
    <!-- تحذير التعارض -->
    <div *ngIf="conflictsCount() > 0" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-start">
        <hugeicons-icon
          [icon]="Alert01StrokeRounded"
          [size]="20"
          [strokeWidth]="1.5"
          class="text-red-600 ml-2 mt-0.5 flex-shrink-0">
        </hugeicons-icon>
        <div class="flex-1">
          <h4 class="text-sm font-medium text-red-800 mb-2">تم اكتشاف تعارض في الجدول</h4>
          @if (conflictDetails().length > 0) {
            <div class="space-y-2">
              <p class="text-sm text-red-700 mb-3">الشعب المتعارضة:</p>
              @for (conflict of conflictDetails(); track $index) {
                <div class="bg-white border border-red-200 rounded-md p-3">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-red-800">
                      {{ conflict.section1.courseId }} - شعبة {{ conflict.section1.sectionNumber }}
                      <span class="mx-2">⟷</span>
                      {{ conflict.section2.courseId }} - شعبة {{ conflict.section2.sectionNumber }}
                    </span>
                  </div>
                  <div class="text-xs text-red-600">
                    {{ conflict.conflictInfo }}
                  </div>
                </div>
              }
            </div>
          } @else {
            <p class="text-sm text-red-700">
              يوجد {{ getConflictText(conflictsCount()) }} في المواعيد. يرجى مراجعة الجدول وإجراء التعديلات اللازمة.
            </p>
          }
        </div>
      </div>
    </div>

    <div class="flex gap-6">

        <!-- الشريط الجانبي: شعب المقررات -->
        <aside class="w-80 flex-shrink-0">
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm h-full">
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">شعب المقررات</h3>
                            <p class="text-sm text-gray-500 mt-1">اختر الشعب لعرضها في الجدول</p>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button
                                (click)="expandAllCourses()"
                                class="px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-md hover:bg-indigo-100 transition-colors"
                                title="توسيع الكل">
                                توسيع الكل
                            </button>
                            <button
                                (click)="collapseAllCourses()"
                                class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 transition-colors"
                                title="طي الكل">
                                طي الكل
                            </button>
                        </div>
                    </div>


                </div>


                <!-- Courses List Section -->
                <div class="p-4 overflow-y-auto" [style.max-height]="calculateSidebarMaxHeight()">
                    <div *ngIf="uniqueCourses().length === 0" class="text-center py-8">
                        <div class="mx-auto h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                            <hugeicons-icon
                              [icon]="File02StrokeRounded"
                              [size]="24"
                              [strokeWidth]="1.5"
                              class="text-gray-400">
                            </hugeicons-icon>
                        </div>
                        <p class="text-sm text-gray-500">لا توجد مقررات متاحة</p>
                        <p class="text-xs text-gray-400 mt-1">قم برفع ملف الجدول أولاً</p>
                    </div>

                    <div *ngFor="let course of uniqueCourses(); let i = index" class="mb-4">
                        <button (click)="toggleCourse(i)"
                                class="w-full flex items-center justify-between p-3 bg-gray-50
                                 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                                [class.bg-indigo-50]="isCourseExpanded(i)"
                                [class.border-indigo-200]="isCourseExpanded(i)">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <div class="w-4 h-4 rounded" [style.background-color]="getCourseColor(course.code)"></div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900 text-sm">{{ course.code }}</p>
                                    <p class="text-xs text-gray-500 truncate max-w-48">{{ course.name }}</p>
                                </div>
                            </div>
                            <hugeicons-icon
                              [icon]="ArrowDown01StrokeRounded"
                              [size]="20"
                              [strokeWidth]="1.5"
                              class="text-gray-400 transform transition-transform duration-200"
                              [class.rotate-180]="isCourseExpanded(i)">
                            </hugeicons-icon>
                        </button>

                        <div *ngIf="isCourseExpanded(i)" class="mt-2 mb-4 border-r-2 border-gray-200 pr-4">
                            <div class="grid grid-cols-2 gap-2" >
                                <button *ngFor="let section of getSectionsForCourse(course.code)"
                                        (click)="!hasEmptySectionTimeData(section) && toggleSection(section)"
                                        (mouseenter)="!hasEmptySectionTimeData(section) && previewSection2(section)"
                                        (mouseleave)="!hasEmptySectionTimeData(section) && clearPreview(section)"
                                        class="px-3 py-2 text-sm rounded-md border transition-all duration-200 text-center relative"
                                        [class]="getSectionButtonClass(section)"
                                        [disabled]="hasEmptySectionTimeData(section)"
                                        [tuiHint]="hasEmptySectionTimeData(section) ? 'هذه الشعبة لا تحتوي على بيانات مواعيد صحيحة.\nقد تكون بيانات الجدول غير مكتملة أو تحتاج إلى تحديث من النظام الأكاديمي.' : null"
                                     >
                                    <div class="font-medium">{{ section.sectionNumber }}</div>
                                    <div class="text-xs font-semibold text-blue-600 mb-1">{{ getSectionTypeArabic(section.type) }}</div>
                                    <div class="text-xs opacity-75">
                                        @if (section.instructor.name && section.instructor.name.trim()) {
                                            {{ section.instructor.name === 'لم يحدد من الكلية' ? section.instructor.name : section.instructor.name.split(' ')[0] }}
                                        } @else {
                                            المحاضر لم يحدد
                                        }
                                    </div>
                                    <hugeicons-icon *ngIf="hasSectionConflict(section)"
                                                    [icon]="Alert01StrokeRounded"
                                                    [strokeWidth]="1.5"
                                                    [size]="16"
                                                    class="absolute top-1 right-1 text-red-600">
                                    </hugeicons-icon>
                                    <hugeicons-icon *ngIf="hasEmptySectionTimeData(section) && !hasSectionConflict(section)"
                                                    [icon]="Clock05StrokeRounded"
                                                    [strokeWidth]="1.5"
                                                    [size]="14"
                                                    class="absolute top-1 right-1 text-gray-400">
                                    </hugeicons-icon>
                                    <hugeicons-icon *ngIf="section.status === 'closed'"
                                                    [icon]="SquareLock02StrokeRounded"
                                                    [size]="14"
                                                    [strokeWidth]="1.5"
                                                    class="absolute top-1 left-1 text-gray-500">
                                    </hugeicons-icon>
                                </button>
                            </div>

                            <div class="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
                                <span>{{ getSectionText(getSectionsForCourse(course.code).length) }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ getSectionText(getSelectedSectionsForCourse(course.code).length) }} مختارة</span>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="border-t border-gray-200 p-4">
                    <div class="flex justify-between items-center text-sm">
                        <span class="text-gray-600">المجموع:</span>
                        <span class="font-medium text-gray-900">{{ getSectionText(internalSelectedSections().length) }}</span>
                    </div>
                    <button (click)="clearAllSections()" *ngIf="internalSelectedSections().length > 0"
                            class="w-full mt-2 px-3 py-2 text-sm text-red-600 border border-red-200
                               rounded-md hover:bg-red-50 transition-colors">
                        إلغاء تحديد كل الشعب
                    </button>
                </div>
            </div>
        </aside>

      <!-- التقويم -->
      <div class="flex-1">
        <div class="calendar-container">
          <full-calendar #calendar [options]="calendarOptions()"></full-calendar>
        </div>
      </div>

    </div>
  </div>

  <!-- دليل الألوان -->
  <div *ngIf="!loading() && calendarEvents().length > 0" class="mt-6">
    <h4 class="text-sm font-semibold text-gray-900 mb-3">دليل الألوان</h4>
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
      <div *ngFor="let course of uniqueCourses()"
           class="flex items-center space-x-2 space-x-reverse p-2 bg-gray-50 rounded-lg">
        <div class="w-4 h-4 rounded" [style.background-color]="getCourseColor(course.code)"></div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 truncate">{{ course.code }}</p>
          <p class="text-xs text-gray-500 truncate">{{ course.name }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Settings Drawer -->
  <div
    *ngIf="settingsDrawerOpen()"
    class="fixed inset-0 z-50 overflow-hidden settings-backdrop-enter"
    (click)="closeSettingsDrawer()">
    <!-- Backdrop -->
    <div class="absolute inset-0 bg-black bg-opacity-50 transition-opacity"></div>

    <!-- Drawer -->
    <div
      class="absolute right-0 top-0 h-full w-96 bg-white shadow-xl transform transition-transform settings-drawer-enter"
      (click)="$event.stopPropagation()">
      <div class="p-6 h-full overflow-y-auto">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3 space-x-reverse">
            <hugeicons-icon
              [icon]="Settings02StrokeRounded"
              [size]="24"
              [strokeWidth]="1.5"
              class="text-gray-700">
            </hugeicons-icon>
            <h3 class="text-lg font-semibold text-gray-900">إعدادات التقويم</h3>
          </div>
          <button
            (click)="closeSettingsDrawer()"
            class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <hugeicons-icon
              [icon]="Cancel01StrokeRounded"
              [size]="20"
              [strokeWidth]="1.5"
              class="text-gray-500">
            </hugeicons-icon>
          </button>
        </div>

        <!-- Settings Content -->
        <div class="space-y-6">
          <!-- Color Mode Setting -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center space-x-3 space-x-reverse mb-3">
              <hugeicons-icon
                [icon]="PaintBrush01StrokeRounded"
                [size]="20"
                [strokeWidth]="1.5"
                class="text-indigo-600">
              </hugeicons-icon>
              <h4 class="font-medium text-gray-900">نمط الألوان</h4>
            </div>

            <p class="text-sm text-gray-600 mb-4">
              اختر نمط الألوان المفضل لعرض المقررات في التقويم
            </p>

            <div class="space-y-3">
              <!-- Elegant Gradients Option -->
              <button
                (click)="!useBasicColors() || toggleColorMode()"
                [class]="!useBasicColors()
                  ? 'border-indigo-500 bg-indigo-50'
                  : 'border-gray-200 bg-white hover:bg-gray-50'"
                class="w-full p-4 border rounded-lg text-right transition-all duration-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2 space-x-reverse">
                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-purple-500 to-blue-500"></div>
                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-pink-500 to-orange-500"></div>
                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-green-500 to-teal-500"></div>
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">ألوان متدرجة أنيقة</div>
                    <div class="text-sm text-gray-600">تدرجات ملونة جميلة مع تأثيرات بصرية</div>
                  </div>
                </div>
              </button>

              <!-- Basic Colors Option -->
              <button
                (click)="useBasicColors() || toggleColorMode()"
                [class]="useBasicColors()
                  ? 'border-indigo-500 bg-indigo-50'
                  : 'border-gray-200 bg-white hover:bg-gray-50'"
                class="w-full p-4 border rounded-lg text-right transition-all duration-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2 space-x-reverse">
                    <div class="w-4 h-4 rounded bg-blue-500"></div>
                    <div class="w-4 h-4 rounded bg-green-500"></div>
                    <div class="w-4 h-4 rounded bg-yellow-500"></div>
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">ألوان بسيطة</div>
                    <div class="text-sm text-gray-600">ألوان صلبة بسيطة وواضحة</div>
                  </div>
                </div>
              </button>
            </div>
          </div>

          <!-- Optimal Schedule Builder -->
          <div class="border border-gray-200 rounded-lg p-4" [formGroup]="optimizationForm">
            <div class="flex items-center space-x-3 space-x-reverse mb-3">
              <hugeicons-icon
                [icon]="Settings02StrokeRounded"
                [size]="20"
                [strokeWidth]="1.5"
                class="text-green-600">
              </hugeicons-icon>
              <h4 class="font-medium text-gray-900">بناء الجدول الأمثل</h4>
            </div>

            <p class="text-sm text-gray-600 mb-4">
              اختر المقررات التي تريد دراستها هذا الفصل، ثم دع النظام يبني لك جدولاً مثالياً
            </p>

            <div class="space-y-4">
              <!-- Course and Section Type Selection -->
              <div>
                <label class="text-sm font-medium text-gray-700 block mb-2">المقررات والشعب المطلوبة:</label>
                <div class="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3 space-y-3">
                  @for (course of uniqueCourses(); track course.code) {
                    <div class="border-b border-gray-100 pb-2 last:border-b-0">
                      <!-- Course Header -->
                      <div class="flex items-center justify-between mb-2">
                        <div class="flex-1">
                          <span class="font-medium text-gray-900 text-sm">{{ course.code }}</span>
                          <span class="text-gray-500 text-xs block">{{ course.name }}</span>
                        </div>
                        <span class="text-xs text-gray-400">{{ getSectionsForCourse(course.code).length }} شعبة</span>
                      </div>

                      <!-- Section Types for this Course -->
                      <div class="grid grid-cols-2 gap-2 ml-4">
                        @for (sectionType of getSectionTypesForCourse(course.code); track sectionType) {
                          <label class="flex items-center text-xs">
                            <input
                              type="checkbox"
                              class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-1 scale-75"
                              [checked]="isCourseSectionTypeSelected(course.code, sectionType)"
                              (change)="toggleCourseSectionType(course.code, sectionType, $event)">
                            <span class="text-gray-600">{{ getSectionTypeDisplayName(sectionType) }}</span>
                          </label>
                        }
                      </div>
                    </div>
                  }
                </div>
                <div class="mt-2 flex justify-between text-xs text-gray-500">
                  <span>{{ getSelectedCourseSectionTypes().length }} نوع شعبة مختار</span>
                  <div class="space-x-2 space-x-reverse">
                    <button
                      (click)="selectAllCoursesForOptimization()"
                      class="text-indigo-600 hover:text-indigo-700">تحديد الكل</button>
                    <button
                      (click)="clearAllCoursesForOptimization()"
                      class="text-red-600 hover:text-red-700">إلغاء الكل</button>
                  </div>
                </div>
              </div>

              <!-- Optimization Criteria -->
              <div>
                <label class="text-sm font-medium text-gray-700 block mb-2">معايير التحسين:</label>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2"
                           formControlName="compactDays">
                    <span class="text-sm text-gray-600">تجميع المحاضرات في أيام قليلة</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2"
                           formControlName="avoidEarlyMorning">
                    <span class="text-sm text-gray-600">تجنب المحاضرات المبكرة (قبل 8 صباحاً)</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2"
                           formControlName="increaseBreaks">
                    <span class="text-sm text-gray-600">زيادة أوقات الراحة بين المحاضرات</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2"
                           formControlName="chooseBestInstructors">
                    <span class="text-sm text-gray-600">اختيار أفضل المحاضرين (الأعلى تقييماً)</span>
                  </label>
                </div>
              </div>

              <!-- Section Availability Options -->
              <div>
                <label class="text-sm font-medium text-gray-700 block mb-2">خيارات توفر الشعب:</label>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input type="checkbox"
                           class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2"
                           formControlName="includeClosedSections">
                    <span class="text-sm text-gray-600">تضمين الشعب المغلقة</span>
                    <span class="text-xs text-gray-500 mr-2">(قد تفتح لاحقاً)</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox"
                           class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2"
                           formControlName="includeSectionsWithoutSchedules">
                    <span class="text-sm text-gray-600">تضمين الشعب بدون مواعيد</span>
                    <span class="text-xs text-gray-500 mr-2">(بيانات غير مكتملة)</span>
                  </label>
                </div>
              </div>

              <!-- Preferred Time Range -->
              <div>
                <label class="text-sm font-medium text-gray-700 block mb-2">الأوقات المفضلة:</label>
                <div class="grid grid-cols-2 gap-3">
                  <div>
                    <label class="text-xs text-gray-500">من الساعة</label>
                    <select class="w-full text-sm border border-gray-300 rounded-md px-2 py-1"
                            formControlName="preferredStartTime">
                      <option value="07:00">7:00 ص</option>
                      <option value="08:00">8:00 ص</option>
                      <option value="09:00">9:00 ص</option>
                      <option value="10:00">10:00 ص</option>
                    </select>
                  </div>
                  <div>
                    <label class="text-xs text-gray-500">إلى الساعة</label>
                    <select class="w-full text-sm border border-gray-300 rounded-md px-2 py-1"
                            formControlName="preferredEndTime">
                      <option value="15:00">3:00 م</option>
                      <option value="16:00">4:00 م</option>
                      <option value="17:00">5:00 م</option>
                      <option value="18:00">6:00 م</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- Preferred Days -->
              <div>
                <label class="text-sm font-medium text-gray-700 block mb-2">الأيام المفضلة:</label>
                <div class="grid grid-cols-4 gap-2" formGroupName="preferredDays">
                  <label class="flex items-center text-xs">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-1"
                           formControlName="sunday">
                    <span>الأحد</span>
                  </label>
                  <label class="flex items-center text-xs">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-1"
                           formControlName="monday">
                    <span>الاثنين</span>
                  </label>
                  <label class="flex items-center text-xs">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-1"
                           formControlName="tuesday">
                    <span>الثلاثاء</span>
                  </label>
                  <label class="flex items-center text-xs">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-1"
                           formControlName="wednesday">
                    <span>الأربعاء</span>
                  </label>
                  <label class="flex items-center text-xs">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-1"
                           formControlName="thursday">
                    <span>الخميس</span>
                  </label>
                  <label class="flex items-center text-xs">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-1"
                           formControlName="friday">
                    <span>الجمعة</span>
                  </label>
                  <label class="flex items-center text-xs">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-1"
                           formControlName="saturday">
                    <span>السبت</span>
                  </label>
                </div>
              </div>

              <!-- Build Button -->
              <button
                (click)="buildOptimalSchedule()"
                [disabled]="getSelectedCourseSectionTypes().length === 0"
                [class]="getSelectedCourseSectionTypes().length === 0
                  ? 'w-full bg-gray-400 text-white font-medium py-2 px-4 rounded-lg cursor-not-allowed'
                  : 'w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors'">
                🚀 بناء الجدول الأمثل ({{ getSelectedCourseSectionTypes().length }} نوع شعبة)
              </button>
            </div>
          </div>


          <!-- Filtering Settings -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center space-x-3 space-x-reverse mb-3">
              <hugeicons-icon
                [icon]="Settings02StrokeRounded"
                [size]="20"
                [strokeWidth]="1.5"
                class="text-orange-600">
              </hugeicons-icon>
              <h4 class="font-medium text-gray-900">تصفية المحتوى</h4>
            </div>

            <div class="space-y-3">
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2">
                <span class="text-sm text-gray-600">إخفاء الشعب المغلقة</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2">
                <span class="text-sm text-gray-600">إخفاء الشعب بدون مواعيد</span>
              </label>

              <div>
                <label class="text-sm font-medium text-gray-700 block mb-2">تصفية حسب نوع الشعبة:</label>
                <div class="space-y-1">
                  <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2" checked>
                    <span class="text-xs text-gray-600">نظري</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2" checked>
                    <span class="text-xs text-gray-600">عملي</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2" checked>
                    <span class="text-xs text-gray-600">تطبيقي</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 ml-2" checked>
                    <span class="text-xs text-gray-600">تدريب</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
